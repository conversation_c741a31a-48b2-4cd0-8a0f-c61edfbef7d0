/**
 * 搜索过滤模块
 * 
 * 职责: 处理设备搜索、状态过滤、排序等数据筛选功能
 * 依赖: config.js, utils.js, event-system.js
 * 作者: Device Tracking System
 * 创建时间: 2025-01-30
 */

import { UI_CONFIG } from './config.js';
import { DateUtils, DOMUtils } from './utils.js';
import { EVENT_TYPES, emit } from './event-system.js';

// ==================== 搜索过滤器类 ====================
/**
 * 搜索过滤器类
 * 处理设备数据的搜索、过滤和排序功能
 */
export class SearchFilter {
    constructor() {
        // 当前过滤条件
        this.currentFilters = {
            search: '',
            status: '',
            workMode: '',
            sortBy: UI_CONFIG.TABLE.DEFAULT_SORT_BY,
            sortOrder: UI_CONFIG.TABLE.DEFAULT_SORT_ORDER
        };

        // 防抖定时器
        this.searchDebounceTimer = null;
        
        // DOM元素缓存
        this.elements = {
            searchInput: null,
            statusFilter: null,
            workModeFilter: null,
            sortButtons: null,
            clearFiltersBtn: null,
            imsiDropdownContainer: null,
            imsiOptionsContainer: null
        };

        // IMSI下拉框状态
        this.imsiDropdown = {
            isOpen: false,
            availableImsis: [],
            filteredImsis: [],
            selectedIndex: -1,
            lastRenderedContent: null  // 跟踪上次渲染的内容
        };

        this.initElements();
        this.bindEvents();
    }

    /**
     * 初始化DOM元素引用
     * @private
     */
    initElements() {
        this.elements.searchInput = document.getElementById('searchInput');
        this.elements.statusFilter = document.getElementById('statusFilter');
        this.elements.workModeFilter = document.getElementById('workModeFilter'); // 可能不存在
        this.elements.sortButtons = document.querySelectorAll('[data-sort]');
        this.elements.clearFiltersBtn = document.getElementById('clearFiltersBtn'); // 修正ID
        this.elements.clearSearchBtn = document.getElementById('clearSearchBtn');
        this.elements.imsiDropdownContainer = document.getElementById('imsiDropdownContainer');
        this.elements.imsiOptionsContainer = document.getElementById('imsiOptionsContainer');

        // 调试：检查元素是否存在
        console.log('[SearchFilter] 元素初始化状态:', {
            searchInput: !!this.elements.searchInput,
            statusFilter: !!this.elements.statusFilter,
            workModeFilter: !!this.elements.workModeFilter,
            clearFiltersBtn: !!this.elements.clearFiltersBtn
        });
    }

    /**
     * 绑定事件监听器
     * @private
     */
    bindEvents() {
        // IMSI搜索输入框事件
        if (this.elements.searchInput) {
            // 输入事件：实时搜索和过滤，显示下拉框
            this.elements.searchInput.addEventListener('input', (event) => {
                const value = event.target.value;
                this.handleSearchInput(value);
                this.filterImsiDropdown(value);
                this.updateClearButtonVisibility(value);

                // 如果有输入内容或者聚焦状态，显示下拉框
                if (value.trim() !== '' || document.activeElement === this.elements.searchInput) {
                    this.showImsiDropdown();
                }
            });

            // 聚焦事件：显示下拉框
            this.elements.searchInput.addEventListener('focus', () => {
                this.showImsiDropdown();
            });

            // 键盘导航
            this.elements.searchInput.addEventListener('keydown', (event) => {
                this.handleKeyboardNavigation(event);
            });

            // 点击事件：显示下拉框
            this.elements.searchInput.addEventListener('click', () => {
                this.showImsiDropdown();
            });
        }

        // 清除搜索按钮事件
        if (this.elements.clearSearchBtn) {
            this.elements.clearSearchBtn.addEventListener('click', (event) => {
                event.preventDefault();
                event.stopPropagation();
                this.clearSearch();
            });
        }

        // 点击下拉容器外部时关闭下拉列表
        document.addEventListener('click', (event) => {
            if (this.elements.imsiDropdownContainer &&
                !this.elements.imsiDropdownContainer.contains(event.target)) {
                this.hideImsiDropdown();
            }
        });

        // 状态过滤器事件
        if (this.elements.statusFilter) {
            this.elements.statusFilter.addEventListener('change', (event) => {
                this.handleStatusFilter(event.target.value);
            });
        }

        // 工作模式过滤器事件（如果存在）
        if (this.elements.workModeFilter) {
            this.elements.workModeFilter.addEventListener('change', (event) => {
                this.handleWorkModeFilter(event.target.value);
            });
        } else {
            console.log('[SearchFilter] 工作模式过滤器不存在，跳过事件绑定');
        }



        // 排序按钮事件
        this.elements.sortButtons.forEach(button => {
            button.addEventListener('click', () => {
                const sortBy = button.dataset.sort;
                this.handleSort(sortBy);
            });
        });

        // 清除过滤器按钮事件
        if (this.elements.clearFiltersBtn) {
            this.elements.clearFiltersBtn.addEventListener('click', () => {
                this.clearAllFilters();
            });
        } else {
            console.log('[SearchFilter] 清除过滤器按钮不存在，跳过事件绑定');
        }
    }

    /**
     * 处理搜索输入（防抖）
     * @param {string} searchTerm 搜索词
     * @private
     */
    handleSearchInput(searchTerm) {
        // 清除之前的定时器
        if (this.searchDebounceTimer) {
            clearTimeout(this.searchDebounceTimer);
        }

        // 设置新的防抖定时器
        this.searchDebounceTimer = setTimeout(() => {
            this.setSearchTerm(searchTerm);
        }, UI_CONFIG.SEARCH.DEBOUNCE_DELAY);
    }

    /**
     * 设置搜索词
     * @param {string} searchTerm 搜索词
     */
    setSearchTerm(searchTerm) {
        if (this.currentFilters.search !== searchTerm) {
            this.currentFilters.search = searchTerm;
            this.emitFilterChange('search');
        }
    }

    /**
     * 处理状态过滤
     * @param {string} status 状态值
     * @private
     */
    handleStatusFilter(status) {
        if (this.currentFilters.status !== status) {
            this.currentFilters.status = status;
            this.emitFilterChange('status');
        }
    }

    /**
     * 处理工作模式过滤
     * @param {string} workMode 工作模式值
     * @private
     */
    handleWorkModeFilter(workMode) {
        if (this.currentFilters.workMode !== workMode) {
            this.currentFilters.workMode = workMode;
            this.emitFilterChange('workMode');
        }
    }



    /**
     * 处理排序
     * @param {string} sortBy 排序字段
     * @private
     */
    handleSort(sortBy) {
        let sortOrder = 'asc';
        
        // 如果点击的是当前排序字段，切换排序方向
        if (this.currentFilters.sortBy === sortBy) {
            sortOrder = this.currentFilters.sortOrder === 'asc' ? 'desc' : 'asc';
        }

        this.currentFilters.sortBy = sortBy;
        this.currentFilters.sortOrder = sortOrder;

        // 更新排序按钮状态
        this.updateSortButtonsState();

        this.emitFilterChange('sort');
    }

    /**
     * 更新排序按钮状态
     * @private
     */
    updateSortButtonsState() {
        this.elements.sortButtons.forEach(button => {
            const sortBy = button.dataset.sort;
            const icon = button.querySelector('i');
            
            if (sortBy === this.currentFilters.sortBy) {
                button.classList.add('btn-active');
                if (icon) {
                    icon.className = this.currentFilters.sortOrder === 'asc' ? 
                        'fas fa-sort-up' : 'fas fa-sort-down';
                }
            } else {
                button.classList.remove('btn-active');
                if (icon) {
                    icon.className = 'fas fa-sort';
                }
            }
        });
    }

    /**
     * 应用搜索过滤
     * @param {Array} devices 设备列表
     * @param {string} searchTerm 搜索词
     * @returns {Array} 过滤后的设备列表
     */
    applySearch(devices, searchTerm) {
        if (!searchTerm || searchTerm.length < UI_CONFIG.SEARCH.MIN_SEARCH_LENGTH) {
            return devices;
        }

        const term = searchTerm.toLowerCase();
        
        return devices.filter(device => {
            // 搜索字段
            const searchFields = [
                device.imsi,
                device.device_name,
                device.position?.location_name || ''
            ];

            return searchFields.some(field => 
                field && field.toLowerCase().includes(term)
            );
        });
    }

    /**
     * 应用状态过滤
     * @param {Array} devices 设备列表
     * @param {string} status 状态值
     * @returns {Array} 过滤后的设备列表
     */
    applyStatusFilter(devices, status) {
        if (!status) return devices;
        
        const statusValue = parseInt(status);
        return devices.filter(device => device.current_status === statusValue);
    }

    /**
     * 应用工作模式过滤
     * @param {Array} devices 设备列表
     * @param {string} workMode 工作模式值
     * @returns {Array} 过滤后的设备列表
     */
    applyWorkModeFilter(devices, workMode) {
        if (!workMode) return devices;
        
        const modeValue = parseInt(workMode);
        return devices.filter(device => device.work_mode === modeValue);
    }



    /**
     * 应用排序
     * @param {Array} devices 设备列表
     * @param {string} sortBy 排序字段
     * @param {string} sortOrder 排序方向
     * @returns {Array} 排序后的设备列表
     */
    applySorting(devices, sortBy, sortOrder) {
        const sortedDevices = [...devices];
        
        sortedDevices.sort((a, b) => {
            let valueA, valueB;
            
            switch (sortBy) {
                case 'imsi':
                    valueA = a.imsi;
                    valueB = b.imsi;
                    break;
                case 'device_name':
                    valueA = a.device_name;
                    valueB = b.device_name;
                    break;
                case 'current_status':
                    valueA = a.current_status;
                    valueB = b.current_status;
                    break;
                case 'last_heartbeat_time':
                    valueA = new Date(a.last_heartbeat_time);
                    valueB = new Date(b.last_heartbeat_time);
                    break;
                case 'battery_level':
                    valueA = a.battery_level;
                    valueB = b.battery_level;
                    break;
                default:
                    return 0;
            }

            // 处理字符串比较
            if (typeof valueA === 'string' && typeof valueB === 'string') {
                valueA = valueA.toLowerCase();
                valueB = valueB.toLowerCase();
            }

            let comparison = 0;
            if (valueA > valueB) comparison = 1;
            if (valueA < valueB) comparison = -1;

            return sortOrder === 'desc' ? -comparison : comparison;
        });

        return sortedDevices;
    }

    /**
     * 应用所有过滤条件
     * @param {Array} devices 原始设备列表
     * @returns {Array} 过滤和排序后的设备列表
     */
    applyAllFilters(devices) {
        console.log('[DEBUG] applyAllFilters 开始，输入设备数:', devices.length);
        console.log('[DEBUG] 当前过滤条件:', this.currentFilters);

        let filteredDevices = [...devices];

        // 应用搜索过滤
        if (this.currentFilters.search) {
            console.log('[DEBUG] 应用搜索过滤:', this.currentFilters.search);
            filteredDevices = this.applySearch(filteredDevices, this.currentFilters.search);
            console.log('[DEBUG] 搜索过滤后设备数:', filteredDevices.length);
        }

        // 应用状态过滤
        if (this.currentFilters.status) {
            console.log('[DEBUG] 应用状态过滤:', this.currentFilters.status);
            filteredDevices = this.applyStatusFilter(filteredDevices, this.currentFilters.status);
            console.log('[DEBUG] 状态过滤后设备数:', filteredDevices.length);
        }

        // 应用工作模式过滤
        if (this.currentFilters.workMode) {
            console.log('[DEBUG] 应用工作模式过滤:', this.currentFilters.workMode);
            filteredDevices = this.applyWorkModeFilter(filteredDevices, this.currentFilters.workMode);
            console.log('[DEBUG] 工作模式过滤后设备数:', filteredDevices.length);
        }



        // 应用排序
        console.log('[DEBUG] 应用排序:', this.currentFilters.sortBy, this.currentFilters.sortOrder);
        filteredDevices = this.applySorting(
            filteredDevices,
            this.currentFilters.sortBy,
            this.currentFilters.sortOrder
        );

        console.log('[DEBUG] applyAllFilters 完成，最终设备数:', filteredDevices.length);
        return filteredDevices;
    }

    /**
     * 清除所有过滤条件
     */
    clearAllFilters() {
        // 重置过滤条件
        this.currentFilters = {
            search: '',
            status: '',
            workMode: '',
            dateRange: {
                start: null,
                end: null
            },
            sortBy: UI_CONFIG.TABLE.DEFAULT_SORT_BY,
            sortOrder: UI_CONFIG.TABLE.DEFAULT_SORT_ORDER
        };

        // 重置UI元素
        if (this.elements.searchInput) {
            this.elements.searchInput.value = '';
            this.updateClearButtonVisibility('');
        }
        if (this.elements.statusFilter) {
            this.elements.statusFilter.value = '';
        }
        if (this.elements.workModeFilter) {
            this.elements.workModeFilter.value = '';
        }


        // 更新排序按钮状态
        this.updateSortButtonsState();

        // 发送清除事件
        this.emitFilterChange('clear');

        console.log('[SearchFilter] 已清除所有过滤条件');
    }

    /**
     * 获取当前过滤条件
     * @returns {Object} 当前过滤条件
     */
    getCurrentFilters() {
        return { ...this.currentFilters };
    }

    /**
     * 设置过滤条件
     * @param {Object} filters 过滤条件
     */
    setFilters(filters) {
        const oldFilters = { ...this.currentFilters };

        // 更新过滤条件
        Object.assign(this.currentFilters, filters);

        // 更新UI元素
        this.updateUIElements();

        // 检查是否有变化
        if (JSON.stringify(oldFilters) !== JSON.stringify(this.currentFilters)) {
            this.emitFilterChange('programmatic');
        }
    }

    /**
     * 更新UI元素状态
     * @private
     */
    updateUIElements() {
        if (this.elements.searchInput) {
            this.elements.searchInput.value = this.currentFilters.search;
            this.updateClearButtonVisibility(this.currentFilters.search);
        }
        if (this.elements.statusFilter) {
            this.elements.statusFilter.value = this.currentFilters.status;
        }
        if (this.elements.workModeFilter) {
            this.elements.workModeFilter.value = this.currentFilters.workMode;
        }


        this.updateSortButtonsState();
    }

    /**
     * 检查是否有活动的过滤条件
     * @returns {boolean} 是否有活动过滤条件
     */
    hasActiveFilters() {
        return !!(
            this.currentFilters.search ||
            this.currentFilters.status ||
            this.currentFilters.workMode ||
            this.currentFilters.dateRange.start ||
            this.currentFilters.dateRange.end ||
            this.currentFilters.sortBy !== UI_CONFIG.TABLE.DEFAULT_SORT_BY ||
            this.currentFilters.sortOrder !== UI_CONFIG.TABLE.DEFAULT_SORT_ORDER
        );
    }

    /**
     * 获取过滤结果统计
     * @param {Array} originalDevices 原始设备列表
     * @param {Array} filteredDevices 过滤后设备列表
     * @returns {Object} 过滤统计信息
     */
    getFilterStats(originalDevices, filteredDevices) {
        return {
            total: originalDevices.length,
            filtered: filteredDevices.length,
            hidden: originalDevices.length - filteredDevices.length,
            hasFilters: this.hasActiveFilters()
        };
    }

    /**
     * 发送过滤变化事件
     * @param {string} type 变化类型
     * @private
     */
    emitFilterChange(type) {
        emit(EVENT_TYPES.FILTER_CHANGED, {
            type,
            filters: this.getCurrentFilters(),
            timestamp: Date.now()
        });

        console.log(`[SearchFilter] 过滤条件已变更: ${type}`);
    }

    // ==================== IMSI下拉框相关方法 ====================

    /**
     * 从API加载所有IMSI选项
     * @returns {Promise<void>}
     */
    async loadImsiOptionsFromAPI() {
        try {
            console.log('[SearchFilter] 开始从API加载IMSI选项');

            // 导入API客户端
            const { apiClient } = await import('./api-client.js');

            // 获取所有IMSI列表
            const imsiList = await apiClient.getIMSIList();

            if (!Array.isArray(imsiList) || imsiList.length === 0) {
                console.warn('[SearchFilter] API返回的IMSI列表为空');
                return;
            }

            // 排序IMSI列表
            const sortedImsis = imsiList.sort();

            this.imsiDropdown.availableImsis = sortedImsis;
            this.imsiDropdown.filteredImsis = [...sortedImsis];

            // 更新下拉列表显示
            this.renderImsiDropdown();
            // 重置渲染内容标识，强制下次重新渲染
            this.imsiDropdown.lastRenderedContent = null;

            console.log(`[SearchFilter] IMSI选项从API加载成功: ${sortedImsis.length} 个选项`);
        } catch (error) {
            console.error('[SearchFilter] 从API加载IMSI选项失败:', error);
            // 如果API加载失败，可以考虑回退到旧的方式
        }
    }

    /**
     * 更新IMSI下拉框选项（保留旧方法作为备用）
     * @param {Array} devices 设备列表
     * @param {boolean} forceUpdate 是否强制更新（默认false，只在初始化时更新）
     * @deprecated 建议使用 loadImsiOptionsFromAPI() 方法
     */
    updateImsiOptions(devices, forceUpdate = false) {
        if (!devices || !Array.isArray(devices)) {
            console.warn('[SearchFilter] 无效的设备数据，无法更新IMSI选项');
            return;
        }

        // 如果已经通过API加载过IMSI选项，且不强制更新，直接返回
        if (this.imsiDropdown.availableImsis.length > 0 && !forceUpdate) {
            console.log('[SearchFilter] IMSI选项已初始化，跳过更新');
            return;
        }

        // 从设备数据中提取IMSI
        const imsis = devices
            .map(device => device.imsi)
            .filter(imsi => imsi && imsi.trim() !== '')
            .filter((imsi, index, array) => array.indexOf(imsi) === index) // 去重
            .sort(); // 排序

        if (imsis.length === 0) {
            console.warn('[SearchFilter] 没有找到有效的IMSI数据');
            return;
        }

        this.imsiDropdown.availableImsis = imsis;
        this.imsiDropdown.filteredImsis = [...imsis];

        // 更新下拉列表显示
        this.renderImsiDropdown();
        // 重置渲染内容标识，强制下次重新渲染
        this.imsiDropdown.lastRenderedContent = null;

        console.log(`[SearchFilter] IMSI选项已更新: ${imsis.length} 个选项`);
    }

    /**
     * 检查是否需要重新渲染IMSI下拉列表
     * @private
     */
    renderImsiDropdownIfNeeded() {
        if (!this.elements.imsiOptionsContainer) return;

        // 生成当前内容的标识
        const currentContent = JSON.stringify(this.imsiDropdown.filteredImsis);

        // 如果内容没有变化，则不重新渲染
        if (this.imsiDropdown.lastRenderedContent === currentContent) {
            return;
        }

        // 内容发生了变化，执行重新渲染
        this.renderImsiDropdown();
        this.imsiDropdown.lastRenderedContent = currentContent;
    }

    /**
     * 渲染IMSI下拉列表
     * @private
     */
    renderImsiDropdown() {
        if (!this.elements.imsiOptionsContainer) return;

        const container = this.elements.imsiOptionsContainer;
        container.innerHTML = '';

        if (this.imsiDropdown.filteredImsis.length === 0) {
            const emptyItem = document.createElement('li');
            emptyItem.innerHTML = '<a class="text-gray-500 cursor-default">暂无匹配的IMSI</a>';
            container.appendChild(emptyItem);
            return;
        }

        this.imsiDropdown.filteredImsis.forEach((imsi, index) => {
            const listItem = document.createElement('li');
            listItem.innerHTML = `
                <a class="imsi-option cursor-pointer" data-imsi="${imsi}" data-index="${index}" tabindex="-1">
                    ${imsi}
                </a>
            `;

            // 添加点击事件
            const link = listItem.querySelector('.imsi-option');
            link.addEventListener('click', (event) => {
                event.preventDefault();
                event.stopPropagation();
                this.selectImsi(imsi);
            });

            container.appendChild(listItem);
        });

        // 重置选中索引
        this.imsiDropdown.selectedIndex = -1;
    }

    /**
     * 过滤IMSI下拉选项
     * @param {string} searchTerm 搜索词
     * @private
     */
    filterImsiDropdown(searchTerm) {
        if (!searchTerm || searchTerm.trim() === '') {
            this.imsiDropdown.filteredImsis = [...this.imsiDropdown.availableImsis];
        } else {
            const term = searchTerm.toLowerCase();
            this.imsiDropdown.filteredImsis = this.imsiDropdown.availableImsis
                .filter(imsi => imsi.toLowerCase().includes(term));
        }

        this.renderImsiDropdown();
        // 重置渲染内容标识，因为过滤内容已改变
        this.imsiDropdown.lastRenderedContent = null;
    }

    /**
     * 显示IMSI下拉列表
     * @private
     */
    showImsiDropdown() {
        if (!this.elements.imsiDropdownContainer) return;

        const dropdownList = this.elements.imsiDropdownContainer.querySelector('.dropdown-content');
        if (dropdownList) {
            dropdownList.classList.add('show');
            dropdownList.style.display = 'block';
        }

        this.imsiDropdown.isOpen = true;
        this.renderImsiDropdownIfNeeded();
    }

    /**
     * 隐藏IMSI下拉列表
     * @private
     */
    hideImsiDropdown() {
        if (!this.elements.imsiDropdownContainer) return;

        const dropdownList = this.elements.imsiDropdownContainer.querySelector('.dropdown-content');
        if (dropdownList) {
            dropdownList.classList.remove('show');
            dropdownList.style.display = 'none';
        }

        this.imsiDropdown.isOpen = false;
        this.imsiDropdown.selectedIndex = -1;
        this.clearOptionHighlight();
    }

    /**
     * 切换IMSI下拉列表显示状态
     * @private
     */
    toggleImsiDropdown() {
        if (this.imsiDropdown.isOpen) {
            this.hideImsiDropdown();
        } else {
            this.showImsiDropdown();
        }
    }

    /**
     * 选择IMSI
     * @param {string} imsi 选中的IMSI
     * @private
     */
    selectImsi(imsi) {
        // 更新搜索框的值
        if (this.elements.searchInput) {
            this.elements.searchInput.value = imsi;
            this.updateClearButtonVisibility(imsi);
        }

        this.setSearchTerm(imsi);
        this.hideImsiDropdown();

        console.log(`[SearchFilter] 已选择IMSI: ${imsi}`);
    }

    /**
     * 处理键盘导航
     * @param {KeyboardEvent} event 键盘事件
     * @private
     */
    handleKeyboardNavigation(event) {
        if (!this.imsiDropdown.isOpen) return;

        const options = this.elements.imsiOptionsContainer?.querySelectorAll('.imsi-option');
        if (!options || options.length === 0) return;

        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                this.imsiDropdown.selectedIndex = Math.min(
                    this.imsiDropdown.selectedIndex + 1,
                    options.length - 1
                );
                this.highlightOption(this.imsiDropdown.selectedIndex);
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.imsiDropdown.selectedIndex = Math.max(
                    this.imsiDropdown.selectedIndex - 1,
                    0
                );
                this.highlightOption(this.imsiDropdown.selectedIndex);
                break;
            case 'Enter':
                event.preventDefault();
                if (this.imsiDropdown.selectedIndex >= 0 &&
                    this.imsiDropdown.selectedIndex < this.imsiDropdown.filteredImsis.length) {
                    const imsi = this.imsiDropdown.filteredImsis[this.imsiDropdown.selectedIndex];
                    this.selectImsi(imsi);
                }
                break;
            case 'Escape':
                event.preventDefault();
                this.hideImsiDropdown();
                if (this.elements.searchInput) {
                    this.elements.searchInput.focus();
                }
                break;
        }
    }

    /**
     * 高亮指定选项
     * @param {number} index 要高亮的索引
     * @private
     */
    highlightOption(index) {
        const options = this.elements.imsiOptionsContainer?.querySelectorAll('.imsi-option');
        if (!options) return;

        // 移除所有焦点
        this.clearOptionHighlight();

        // 聚焦到指定选项
        if (options[index]) {
            // 添加键盘导航标记类
            options[index].classList.add('keyboard-focused');
            options[index].focus();
            options[index].scrollIntoView({ block: 'nearest' });
        }
    }

    /**
     * 清除所有选项的高亮
     * @private
     */
    clearOptionHighlight() {
        const options = this.elements.imsiOptionsContainer?.querySelectorAll('.imsi-option');
        if (options) {
            options.forEach(option => {
                // 只移除键盘导航标记，不调用blur以避免干扰鼠标hover
                option.classList.remove('keyboard-focused');
                // 只有当元素确实有焦点时才blur
                if (document.activeElement === option) {
                    option.blur();
                }
            });
        }
    }

    /**
     * 销毁搜索过滤器
     */
    destroy() {
        // 清除防抖定时器
        if (this.searchDebounceTimer) {
            clearTimeout(this.searchDebounceTimer);
        }

        // 清空元素引用
        this.elements = {};

        console.log('[SearchFilter] 搜索过滤器已销毁');
    }

    /**
     * 更新清除按钮的显示状态
     * @param {string} value 输入框的值
     * @private
     */
    updateClearButtonVisibility(value) {
        if (this.elements.clearSearchBtn) {
            if (value && value.trim() !== '') {
                this.elements.clearSearchBtn.classList.remove('hidden');
            } else {
                this.elements.clearSearchBtn.classList.add('hidden');
            }
        }
    }

    /**
     * 清除搜索输入框
     * @public
     */
    clearSearch() {
        if (this.elements.searchInput) {
            this.elements.searchInput.value = '';
            this.elements.searchInput.focus();
            this.updateClearButtonVisibility('');
            this.handleSearchInput('');
            this.hideImsiDropdown();
            console.log('[SearchFilter] 搜索输入框已清除');
        }
    }
}

// ==================== 全局搜索过滤器实例 ====================
export const searchFilter = new SearchFilter();

// ==================== 便捷函数 ====================
/**
 * 应用所有过滤条件的便捷函数
 * @param {Array} devices 设备列表
 * @returns {Array} 过滤后的设备列表
 */
export const applyAllFilters = (devices) => searchFilter.applyAllFilters(devices);

/**
 * 清除所有过滤条件的便捷函数
 */
export const clearAllFilters = () => searchFilter.clearAllFilters();

/**
 * 获取当前过滤条件的便捷函数
 * @returns {Object} 当前过滤条件
 */
export const getCurrentFilters = () => searchFilter.getCurrentFilters();

/**
 * 设置过滤条件的便捷函数
 * @param {Object} filters 过滤条件
 */
export const setFilters = (filters) => searchFilter.setFilters(filters);

/**
 * 检查是否有活动过滤条件的便捷函数
 * @returns {boolean} 是否有活动过滤条件
 */
export const hasActiveFilters = () => searchFilter.hasActiveFilters();

// ==================== 默认导出 ====================
export default searchFilter;
