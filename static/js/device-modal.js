/**
 * 设备详情弹窗模块
 * 
 * 职责: 处理设备详情弹窗的显示、数据填充、状态历史时间线渲染等功能
 * 依赖: config.js, utils.js, event-system.js
 * 作者: Device Tracking System
 * 创建时间: 2025-01-30
 */

import { DateUtils, StatusUtils, DataUtils } from './utils.js';
import { EVENT_TYPES, emit } from './event-system.js';
import { apiClient } from './api-client.js';

// ==================== 设备详情弹窗类 ====================
/**
 * 设备详情弹窗类
 * 管理设备详情弹窗的显示和数据更新
 */
export class DeviceModal {
    constructor() {
        this.currentDevice = null;
        this.isVisible = false;
        
        // DOM元素缓存
        this.elements = {
            modal: null,
            modalTitle: null,
            modalContent: null,
            closeButton: null,
            backdrop: null
        };
        
        this.initElements();
        this.bindEvents();
    }

    /**
     * 初始化DOM元素引用
     * @private
     */
    initElements() {
        this.elements.modal = document.getElementById('deviceDetailModal');
        this.elements.modalTitle = document.querySelector('#deviceDetailModal h3');
        this.elements.modalContent = document.getElementById('deviceDetailContent');
        this.elements.closeButton = document.querySelector('#deviceDetailModal .btn-close');
        this.elements.backdrop = document.querySelector('#deviceDetailModal .modal-backdrop');
    }

    /**
     * 绑定事件监听器
     * @private
     */
    bindEvents() {
        // 关闭按钮事件
        if (this.elements.closeButton) {
            this.elements.closeButton.addEventListener('click', () => {
                this.hide();
            });
        }

        // 背景点击关闭
        if (this.elements.backdrop) {
            this.elements.backdrop.addEventListener('click', () => {
                this.hide();
            });
        }

        // ESC键关闭
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.isVisible) {
                this.hide();
            }
        });

        // 监听设备选择事件
        window.addEventListener(EVENT_TYPES.DEVICE_SELECTED, (event) => {
            this.show(event.detail);
        });

        // 监听设备详情更新事件
        window.addEventListener(EVENT_TYPES.DEVICE_DETAIL_UPDATED, (event) => {
            if (this.isVisible && this.currentDevice && 
                this.currentDevice.imsi === event.detail.imsi) {
                this.updateContent(event.detail.device);
            }
        });
    }

    /**
     * 显示设备详情弹窗
     * @param {Object} device 设备数据
     */
    show(device) {
        if (!device || !this.elements.modal) {
            console.error('[DeviceModal] 无效的设备数据或弹窗元素');
            return;
        }

        this.currentDevice = device;
        this.isVisible = true;

        // 更新弹窗内容
        this.updateContent(device);

        // 显示弹窗
        this.elements.modal.showModal();
        document.body.classList.add('modal-open');

        // 发送显示事件
        emit(EVENT_TYPES.DEVICE_MODAL_SHOWN, {
            imsi: device.imsi,
            timestamp: Date.now()
        });

        console.log(`[DeviceModal] 显示设备详情: ${device.imsi}`);
    }

    /**
     * 隐藏设备详情弹窗
     */
    hide() {
        if (!this.isVisible) return;

        this.isVisible = false;
        
        if (this.elements.modal) {
            this.elements.modal.close();
        }
        
        document.body.classList.remove('modal-open');

        // 发送隐藏事件
        emit(EVENT_TYPES.DEVICE_MODAL_HIDDEN, {
            imsi: this.currentDevice?.imsi,
            timestamp: Date.now()
        });

        console.log('[DeviceModal] 隐藏设备详情弹窗');
        
        // 清空当前设备
        setTimeout(() => {
            this.currentDevice = null;
        }, 300); // 等待动画完成
    }

    /**
     * 更新弹窗内容
     * @param {Object} device 设备数据
     */
    updateContent(device) {
        if (!device || !this.elements.modalContent) return;

        this.currentDevice = device;

        // 更新标题
        if (this.elements.modalTitle) {
            this.elements.modalTitle.textContent = `设备详情 - ${device.imsi}`;
        }

        // 生成弹窗内容HTML
        const contentHTML = this.generateModalContent(device);

        // 更新内容容器
        this.elements.modalContent.innerHTML = contentHTML;

        // 异步加载最近状态变化
        this.loadLatestStatusChange(device.imsi);

        console.log(`[DeviceModal] 已更新设备详情: ${device.imsi}`);
    }

    /**
     * 生成弹窗内容HTML
     * @param {Object} device 设备数据
     * @returns {string} HTML内容
     * @private
     */
    generateModalContent(device) {
        return `
            <h3 class="font-bold text-lg mb-4">设备详情 - ${this.escapeHtml(device.imsi)}</h3>
            
            <!-- 设备基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-base-200">
                    <div class="card-body p-4">
                        <h4 class="card-title text-base">基本信息</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-base-content/70">设备名称:</span>
                                <span class="font-medium">${this.escapeHtml(device.device_name)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">IMSI:</span>
                                <span class="font-medium">${this.escapeHtml(device.imsi)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">当前状态:</span>
                                <span class="badge ${device.current_status === 1 ? 'badge-success' : 'badge-error'} badge-sm">
                                    ${StatusUtils.getStatusText(device.current_status)}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">工作模式:</span>
                                <span class="badge badge-outline badge-sm">
                                    ${StatusUtils.getWorkModeText(device.work_mode)}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card bg-base-200">
                    <div class="card-body p-4">
                        <h4 class="card-title text-base">设备状态</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-base-content/70">电池电量:</span>
                                <div class="flex items-center gap-2">
                                    <div class="radial-progress text-primary" style="--value:${device.battery_level};" role="progressbar">
                                        <span class="text-xs">${device.battery_level}%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">信号强度:</span>
                                <div class="flex items-center gap-2">
                                    <div class="rating rating-sm">
                                        ${this.renderSignalStrength(device.signal_strength)}
                                    </div>
                                    <span class="text-xs">${device.signal_strength}/5</span>
                                </div>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">温度:</span>
                                <span class="font-medium">${device.temperature}°C</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">固件版本:</span>
                                <span class="font-medium">${this.escapeHtml(device.firmware_version)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 位置信息 -->
            <div class="card bg-base-200 mb-6">
                <div class="card-body p-4">
                    <h4 class="card-title text-base">位置信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-base-content/70">当前位置:</span>
                                <span class="font-medium">${this.escapeHtml(device.position.location_name)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">经纬度:</span>
                                <span class="font-medium">${device.position.latitude.toFixed(4)}, ${device.position.longitude.toFixed(4)}</span>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-base-content/70">当前速度:</span>
                                <span class="font-medium">${device.speed >= 0 ? device.speed + ' km/h' : '未知'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">总里程:</span>
                                <span class="font-medium">${DataUtils.formatNumber(device.mileage, 1)} km</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务信息 -->
            ${device.task_status.task_id > 0 ? `
            <div class="card bg-base-200 mb-6">
                <div class="card-body p-4">
                    <h4 class="card-title text-base">任务信息</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-base-content/70">任务ID:</span>
                            <span class="font-medium">#${device.task_status.task_id}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">任务状态:</span>
                            <span class="badge ${device.task_status.step === 2 ? 'badge-success' : device.task_status.step === 1 ? 'badge-warning' : 'badge-error'} badge-sm">
                                ${StatusUtils.getTaskStatusText(device.task_status.step)}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">进度:</span>
                            <div class="flex items-center gap-2">
                                <progress class="progress progress-primary w-20" value="${device.task_status.progress}" max="100"></progress>
                                <span class="text-xs">${device.task_status.progress}%</span>
                            </div>
                        </div>
                        ${device.task_status.current_station ? `
                        <div class="flex justify-between">
                            <span class="text-base-content/70">路线:</span>
                            <span class="font-medium">${this.escapeHtml(device.task_status.current_station)} → ${this.escapeHtml(device.task_status.target_station)}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
            ` : ''}

            ${this.renderTimeline(device)}
        `;
    }

    /**
     * 渲染状态历史时间线
     * @param {Object} device 设备数据
     * @returns {string} 时间线HTML
     * @private
     */
    renderTimeline(device) {
        return `
            <!-- 设备状态历史时间线 -->
            <div class="mt-6">
                <h4 class="text-lg font-bold mb-4">设备状态历史</h4>
                <ul class="timeline timeline-vertical timeline-compact" id="device-timeline-${device.imsi}">
                    <!-- 设备当前状态 -->
                    <li>
                        <div class="timeline-start text-sm text-base-content/70">
                            ${DateUtils.format(device.last_heartbeat_time, 'datetime')}
                        </div>
                        <div class="timeline-middle">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                 class="w-5 h-5 ${device.current_status === 1 ? 'text-success' : 'text-error'}">
                                ${device.current_status === 1 ?
                                    '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />' :
                                    '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5c0 .414.336.75.75.75h4a.75.75 0 000-1.5h-3.25V5z" clip-rule="evenodd" />'
                                }
                            </svg>
                        </div>
                        <div class="timeline-end timeline-box">
                            <div class="font-medium">${device.current_status === 1 ? '设备在线' : '设备离线'}</div>
                            <div class="text-sm text-base-content/70">
                                最后心跳: ${DateUtils.format(device.last_heartbeat_time, 'relative')}
                            </div>
                        </div>
                        <hr class="${device.current_status === 1 ? 'bg-success' : 'bg-error'}" />
                    </li>

                    <!-- 最近状态变化占位符 -->
                    <li id="latest-status-change-${device.imsi}" style="display: none;">
                        <!-- 将通过JavaScript动态填充 -->
                    </li>

                    <!-- 位置更新 -->
                    ${device.position.latitude !== 0 || device.position.longitude !== 0 ? `
                    <li>
                        <hr class="bg-info" />
                        <div class="timeline-start text-sm text-base-content/70">
                            刚刚
                        </div>
                        <div class="timeline-middle">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-info">
                                <path fill-rule="evenodd" d="M9.69 18.933l.003.001C9.89 19.02 10 19 10 19s.11.02.308-.066l.002-.001.006-.003.018-.008a5.741 5.741 0 00.281-.14c.186-.096.446-.24.757-.433.62-.384 1.445-.966 2.274-1.765C15.302 14.988 17 12.493 17 9A7 7 0 103 9c0 3.492 1.698 5.988 3.355 7.584a13.731 13.731 0 002.273 1.765 11.842 11.842 0 00.757.433 5.741 5.741 0 00.281.14l.018.008.006.003zM10 11.25a2.25 2.25 0 100-4.5 2.25 2.25 0 000 4.5z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="timeline-end timeline-box">
                            <div class="font-medium">位置更新</div>
                            <div class="text-sm text-base-content/70">
                                ${this.escapeHtml(device.position.location_name)}
                            </div>
                            <div class="text-xs text-base-content/50">
                                速度: ${device.speed >= 0 ? device.speed + ' km/h' : '未知'}
                            </div>
                        </div>
                        <hr class="bg-info" />
                    </li>
                    ` : ''}

                    <!-- 任务状态 -->
                    ${device.task_status.task_id > 0 ? `
                    <li>
                        <hr class="${device.task_status.step === 2 ? 'bg-success' : device.task_status.step === 1 ? 'bg-warning' : 'bg-base-content/30'}" />
                        <div class="timeline-start text-sm text-base-content/70">
                            任务进行中
                        </div>
                        <div class="timeline-middle">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                 class="w-5 h-5 ${device.task_status.step === 2 ? 'text-success' : device.task_status.step === 1 ? 'text-warning' : 'text-base-content/30'}">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="timeline-end timeline-box">
                            <div class="font-medium">任务 #${device.task_status.task_id}</div>
                            <div class="text-sm text-base-content/70">
                                ${StatusUtils.getTaskStatusText(device.task_status.step)} - ${device.task_status.progress}%
                            </div>
                            ${device.task_status.current_station ? `
                            <div class="text-xs text-base-content/50">
                                ${this.escapeHtml(device.task_status.current_station)} → ${this.escapeHtml(device.task_status.target_station)}
                            </div>
                            ` : ''}
                        </div>
                        <hr class="${device.task_status.step === 2 ? 'bg-success' : device.task_status.step === 1 ? 'bg-warning' : 'bg-base-content/30'}" />
                    </li>
                    ` : `
                    <li>
                        <hr class="bg-base-content/30" />
                        <div class="timeline-start text-sm text-base-content/70">
                            当前状态
                        </div>
                        <div class="timeline-middle">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-base-content/30">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5c0 .414.336.75.75.75h4a.75.75 0 000-1.5h-3.25V5z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="timeline-end timeline-box">
                            <div class="font-medium">空闲状态</div>
                            <div class="text-sm text-base-content/70">
                                工作模式: ${StatusUtils.getWorkModeText(device.work_mode)}
                            </div>
                        </div>
                        <hr class="bg-base-content/30" />
                    </li>
                    `}

                    <!-- 设备信息 -->
                    <li>
                        <hr class="bg-primary" />
                        <div class="timeline-start text-sm text-base-content/70">
                            设备信息
                        </div>
                        <div class="timeline-middle">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-primary">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="timeline-end timeline-box">
                            <div class="font-medium">设备状态</div>
                            <div class="text-sm text-base-content/70 space-y-1">
                                <div>电池: ${device.battery_level}%</div>
                                <div>信号: ${device.signal_strength}/5</div>
                                <div>温度: ${device.temperature}°C</div>
                                <div>版本: ${this.escapeHtml(device.firmware_version)}</div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        `;
    }

    /**
     * 渲染信号强度
     * @param {number} strength 信号强度 (1-5)
     * @returns {string} 信号强度HTML
     * @private
     */
    renderSignalStrength(strength) {
        let html = '';
        for (let i = 1; i <= 5; i++) {
            const filled = i <= strength;
            html += `<input type="radio" class="mask mask-star-2 bg-${filled ? 'warning' : 'base-300'}" disabled />`;
        }
        return html;
    }

    /**
     * HTML转义
     * @param {string} text 文本
     * @returns {string} 转义后的文本
     * @private
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 异步加载最近状态变化
     * @param {string} imsi 设备IMSI
     * @private
     */
    async loadLatestStatusChange(imsi) {
        try {
            const latestChange = await apiClient.getLatestStatusChange(imsi);

            if (latestChange && this.isVisible && this.currentDevice?.imsi === imsi) {
                this.renderLatestStatusChange(imsi, latestChange);
            }
        } catch (error) {
            console.warn(`[DeviceModal] 获取最近状态变化失败: ${imsi}`, error);
            // 静默失败，不影响主要功能
        }
    }

    /**
     * 渲染最近状态变化到时间线
     * @param {string} imsi 设备IMSI
     * @param {Object} statusChange 状态变化数据
     * @private
     */
    renderLatestStatusChange(imsi, statusChange) {
        const placeholder = document.getElementById(`latest-status-change-${imsi}`);
        if (!placeholder) return;

        // 格式化持续时长
        const formatDuration = (seconds) => {
            if (seconds < 60) {
                return `${seconds}秒`;
            } else if (seconds < 3600) {
                return `${Math.floor(seconds / 60)}分钟`;
            } else {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                return hours > 0 ? `${hours}小时${minutes}分钟` : `${minutes}分钟`;
            }
        };

        const statusIcon = statusChange.status === 1 ?
            '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />' :
            '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5c0 .414.336.75.75.75h4a.75.75 0 000-1.5h-3.25V5z" clip-rule="evenodd" />';

        const statusColor = statusChange.status === 1 ? 'text-success' : 'text-error';
        const hrColor = statusChange.status === 1 ? 'bg-success' : 'bg-error';

        placeholder.innerHTML = `
            <hr class="${hrColor}" />
            <div class="timeline-start text-sm text-base-content/70">
                ${DateUtils.format(statusChange.timestamp, 'datetime')}
            </div>
            <div class="timeline-middle">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                     class="w-5 h-5 ${statusColor}">
                    ${statusIcon}
                </svg>
            </div>
            <div class="timeline-end timeline-box">
                <div class="font-medium">${statusChange.status_text}</div>
                <div class="text-sm text-base-content/70">
                    ${statusChange.description}
                </div>
                <div class="text-xs text-base-content/50">
                    持续时长: ${formatDuration(statusChange.duration)}
                </div>
            </div>
            <hr class="${hrColor}" />
        `;

        // 显示元素
        placeholder.style.display = 'block';

        console.log(`[DeviceModal] 已渲染最近状态变化: ${imsi} - ${statusChange.status_text}`);
    }

    /**
     * 检查弹窗是否可见
     * @returns {boolean} 是否可见
     */
    isModalVisible() {
        return this.isVisible;
    }

    /**
     * 获取当前显示的设备
     * @returns {Object|null} 当前设备数据
     */
    getCurrentDevice() {
        return this.currentDevice;
    }

    /**
     * 刷新当前设备数据
     */
    refresh() {
        if (this.currentDevice && this.isVisible) {
            // 发送刷新请求事件
            emit(EVENT_TYPES.DEVICE_DETAIL_REFRESH_REQUESTED, {
                imsi: this.currentDevice.imsi,
                timestamp: Date.now()
            });
        }
    }
}

// ==================== 全局设备弹窗实例 ====================
export const deviceModal = new DeviceModal();

// ==================== 便捷函数 ====================
/**
 * 显示设备详情弹窗的便捷函数
 * @param {Object} device 设备数据
 */
export const showDeviceDetail = (device) => deviceModal.show(device);

/**
 * 隐藏设备详情弹窗的便捷函数
 */
export const hideDeviceDetail = () => deviceModal.hide();

/**
 * 更新设备详情内容的便捷函数
 * @param {Object} device 设备数据
 */
export const updateDeviceDetail = (device) => deviceModal.updateContent(device);

/**
 * 检查弹窗是否可见的便捷函数
 * @returns {boolean} 是否可见
 */
export const isDeviceDetailVisible = () => deviceModal.isModalVisible();

// ==================== 全局函数（兼容旧代码） ====================
// 这些函数用于兼容HTML中的onclick事件
window.showDeviceDetail = (imsi) => {
    // 如果传入的是IMSI字符串，需要先获取设备数据
    if (typeof imsi === 'string') {
        emit(EVENT_TYPES.DEVICE_DETAIL_REQUESTED, { imsi });
    } else {
        // 如果传入的是设备对象
        deviceModal.show(imsi);
    }
};

window.hideDeviceDetail = () => deviceModal.hide();

// ==================== 默认导出 ====================
export default deviceModal;
